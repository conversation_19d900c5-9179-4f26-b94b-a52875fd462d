#!/bin/bash

# MySQL容器修复脚本
# 作者: wujule
# 用于修复MySQL连接问题

set -e

# 配置变量
MYSQL_ROOT_PASSWORD="MyStrongPassword123!"
CONTAINER_NAME="mysql8"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}MySQL容器修复脚本${NC}"
echo ""

# 检查容器是否存在
if ! sudo docker ps -a | grep -q $CONTAINER_NAME; then
    echo -e "${RED}错误: MySQL容器不存在，请先运行安装脚本${NC}"
    exit 1
fi

# 检查容器状态
echo -e "${BLUE}检查容器状态...${NC}"
container_status=$(sudo docker inspect --format='{{.State.Status}}' $CONTAINER_NAME)
echo "容器状态: $container_status"

if [ "$container_status" != "running" ]; then
    echo -e "${YELLOW}容器未运行，正在启动...${NC}"
    sudo docker start $CONTAINER_NAME
    echo "容器已启动"
fi

# 智能等待MySQL完全启动
wait_for_mysql() {
    local max_attempts=60  # 最多等待5分钟
    local attempt=1
    
    echo -e "${BLUE}等待MySQL完全启动...${NC}"
    echo "这可能需要几分钟时间，特别是首次启动时"
    
    while [ $attempt -le $max_attempts ]; do
        printf "尝试连接MySQL... (%d/%d)\r" $attempt $max_attempts
        
        # 使用mysqladmin ping检查MySQL是否准备就绪
        if sudo docker exec $CONTAINER_NAME mysqladmin ping -uroot -p$MYSQL_ROOT_PASSWORD --silent 2>/dev/null; then
            echo -e "\n${GREEN}MySQL已成功启动并准备接受连接！${NC}"
            return 0
        fi
        
        # 检查容器是否还在运行
        if [ "$(sudo docker inspect --format='{{.State.Status}}' $CONTAINER_NAME)" != "running" ]; then
            echo -e "\n${RED}容器已停止运行${NC}"
            return 1
        fi
        
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo -e "\n${RED}MySQL启动超时${NC}"
    return 1
}

# 显示容器日志（最后20行）
show_logs() {
    echo -e "${BLUE}MySQL容器日志（最后20行）:${NC}"
    sudo docker logs --tail 20 $CONTAINER_NAME
    echo ""
}

# 测试MySQL连接
test_mysql_connection() {
    echo -e "${BLUE}测试MySQL连接...${NC}"
    
    # 测试基本连接
    if sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SELECT 'MySQL连接成功!' as message;" 2>/dev/null; then
        echo -e "${GREEN}✓ MySQL连接测试成功${NC}"
    else
        echo -e "${RED}✗ MySQL连接测试失败${NC}"
        return 1
    fi
    
    # 显示版本信息
    echo -e "${BLUE}MySQL版本信息:${NC}"
    sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SELECT VERSION();" 2>/dev/null
    
    # 显示数据库列表
    echo -e "${BLUE}数据库列表:${NC}"
    sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SHOW DATABASES;" 2>/dev/null
    
    # 测试用户连接
    echo -e "${BLUE}测试应用用户连接:${NC}"
    if sudo docker exec $CONTAINER_NAME mysql -uappuser -pAppPassword123! -e "SELECT 'appuser连接成功!' as message;" 2>/dev/null; then
        echo -e "${GREEN}✓ 应用用户连接测试成功${NC}"
    else
        echo -e "${YELLOW}⚠ 应用用户连接失败，但这可能是正常的${NC}"
    fi
}

# 显示连接信息
show_connection_info() {
    echo ""
    echo -e "${GREEN}=== MySQL连接信息 ===${NC}"
    echo "主机: localhost"
    echo "端口: 3306"
    echo "Root密码: $MYSQL_ROOT_PASSWORD"
    echo "数据库: myapp"
    echo "用户: appuser"
    echo "用户密码: AppPassword123!"
    echo ""
    echo -e "${BLUE}连接命令示例:${NC}"
    echo "# 使用Docker客户端连接"
    echo "sudo docker exec -it $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD"
    echo ""
    echo "# 使用外部客户端连接"
    echo "mysql -h localhost -P 3306 -u appuser -pAppPassword123! myapp"
    echo ""
}

# 主修复流程
main() {
    # 显示容器日志
    show_logs
    
    # 等待MySQL启动
    if wait_for_mysql; then
        # 测试连接
        if test_mysql_connection; then
            echo -e "${GREEN}MySQL修复成功！${NC}"
            show_connection_info
        else
            echo -e "${RED}MySQL连接仍然失败${NC}"
            echo "请检查以下内容："
            echo "1. 容器是否有足够的内存"
            echo "2. 数据目录权限是否正确"
            echo "3. 配置文件是否有语法错误"
            show_logs
            exit 1
        fi
    else
        echo -e "${RED}MySQL启动失败${NC}"
        echo "可能的解决方案："
        echo "1. 重启容器: sudo docker restart $CONTAINER_NAME"
        echo "2. 检查系统资源: free -h && df -h"
        echo "3. 查看详细日志: sudo docker logs $CONTAINER_NAME"
        echo "4. 重新创建容器: sudo docker rm $CONTAINER_NAME && ./install-mysql.sh"
        show_logs
        exit 1
    fi
}

# 运行主程序
main "$@"
