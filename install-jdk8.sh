#!/bin/bash

# JDK 1.8 安装脚本 for CentOS 7.9
# 作者: wujule
# 日期: $(date)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}JDK 1.8 安装脚本${NC}"
echo ""

# 检查是否为root用户或有sudo权限
check_privileges() {
    if [[ $EUID -eq 0 ]]; then
        log_info "以root用户运行"
    elif sudo -n true 2>/dev/null; then
        log_info "检测到sudo权限"
    else
        log_error "需要root权限或sudo权限来运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [[ -f /etc/redhat-release ]]; then
        local version=$(cat /etc/redhat-release)
        log_info "系统版本: $version"
    else
        log_error "无法检测系统版本"
        exit 1
    fi
}

# 检查是否已安装Java
check_existing_java() {
    log_info "检查现有Java安装..."
    
    if command -v java &> /dev/null; then
        local java_version=$(java -version 2>&1 | head -n 1)
        log_warning "检测到已安装的Java: $java_version"
        
        echo "现有Java安装："
        java -version
        echo ""
        
        read -p "是否继续安装JDK 1.8？这可能会覆盖现有配置 (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "安装已取消"
            exit 0
        fi
    else
        log_info "未检测到现有Java安装"
    fi
}

# 安装OpenJDK 1.8
install_openjdk8() {
    log_info "开始安装OpenJDK 1.8..."
    
    # 更新yum缓存
    log_info "更新yum缓存..."
    sudo yum update -y
    
    # 安装OpenJDK 1.8
    log_info "安装java-1.8.0-openjdk和开发工具..."
    sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel
    
    log_success "OpenJDK 1.8安装完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置Java环境变量..."
    
    # 查找Java安装路径
    local java_home=$(readlink -f /usr/bin/java | sed "s:bin/java::")
    log_info "Java安装路径: $java_home"
    
    # 创建环境变量配置文件
    log_info "创建Java环境变量配置..."
    sudo tee /etc/profile.d/java.sh > /dev/null <<EOF
#!/bin/bash
# Java Environment Variables
export JAVA_HOME=$java_home
export JRE_HOME=\$JAVA_HOME/jre
export PATH=\$JAVA_HOME/bin:\$JRE_HOME/bin:\$PATH
export CLASSPATH=.:\$JAVA_HOME/lib:\$JRE_HOME/lib
EOF
    
    # 设置执行权限
    sudo chmod +x /etc/profile.d/java.sh
    
    # 加载环境变量
    source /etc/profile.d/java.sh
    
    log_success "环境变量配置完成"
}

# 设置默认Java版本
set_default_java() {
    log_info "设置默认Java版本..."
    
    # 使用alternatives设置默认java
    sudo alternatives --install /usr/bin/java java /usr/lib/jvm/java-1.8.0-openjdk/bin/java 1
    sudo alternatives --install /usr/bin/javac javac /usr/lib/jvm/java-1.8.0-openjdk/bin/javac 1
    
    # 设置为默认
    sudo alternatives --set java /usr/lib/jvm/java-1.8.0-openjdk/bin/java
    sudo alternatives --set javac /usr/lib/jvm/java-1.8.0-openjdk/bin/javac
    
    log_success "默认Java版本设置完成"
}

# 验证安装
verify_installation() {
    log_info "验证Java安装..."
    
    # 重新加载环境变量
    source /etc/profile.d/java.sh
    
    echo -e "${BLUE}Java版本信息:${NC}"
    java -version
    echo ""
    
    echo -e "${BLUE}Java编译器版本:${NC}"
    javac -version
    echo ""
    
    echo -e "${BLUE}环境变量:${NC}"
    echo "JAVA_HOME: $JAVA_HOME"
    echo "JRE_HOME: $JRE_HOME"
    echo "PATH: $PATH"
    echo ""
    
    # 测试Java程序
    log_info "创建测试Java程序..."
    cat > /tmp/HelloWorld.java << 'EOF'
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        System.out.println("Java version: " + System.getProperty("java.version"));
        System.out.println("Java home: " + System.getProperty("java.home"));
    }
}
EOF
    
    # 编译和运行测试程序
    log_info "编译测试程序..."
    cd /tmp
    javac HelloWorld.java
    
    log_info "运行测试程序..."
    java HelloWorld
    
    # 清理测试文件
    rm -f /tmp/HelloWorld.java /tmp/HelloWorld.class
    
    log_success "Java安装验证成功！"
}

# 显示安装信息
show_installation_info() {
    echo ""
    echo -e "${GREEN}==================== JDK 1.8 安装完成 ====================${NC}"
    echo ""
    echo -e "${BLUE}安装信息:${NC}"
    echo "Java版本: $(java -version 2>&1 | head -n 1)"
    echo "Java路径: $JAVA_HOME"
    echo "配置文件: /etc/profile.d/java.sh"
    echo ""
    echo -e "${BLUE}常用命令:${NC}"
    echo "查看Java版本: java -version"
    echo "查看编译器版本: javac -version"
    echo "查看Java路径: echo \$JAVA_HOME"
    echo ""
    echo -e "${BLUE}环境变量已配置:${NC}"
    echo "JAVA_HOME: $JAVA_HOME"
    echo "JRE_HOME: $JRE_HOME"
    echo "PATH: 已添加Java bin目录"
    echo "CLASSPATH: 已配置基本类路径"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo "1. 环境变量已全局配置，新终端会自动生效"
    echo "2. 当前终端需要运行 'source /etc/profile.d/java.sh' 或重新登录"
    echo "3. 如需切换Java版本，可使用 alternatives 命令"
    echo ""
    echo -e "${GREEN}=================================================${NC}"
}

# 主安装流程
main() {
    echo -e "${BLUE}开始安装JDK 1.8...${NC}"
    echo ""
    
    # 检查权限和系统
    check_privileges
    check_system
    check_existing_java
    
    # 确认安装
    echo -e "${YELLOW}即将安装:${NC}"
    echo "- OpenJDK 1.8"
    echo "- Java开发工具包"
    echo "- 环境变量配置"
    echo ""
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi
    
    # 执行安装
    install_openjdk8
    configure_environment
    set_default_java
    verify_installation
    show_installation_info
}

# 运行主程序
main "$@"
