#!/bin/bash

# PostgreSQL Docker安装脚本
# 作者: wujule
# 日期: $(date)

set -e

# 配置变量
POSTGRES_PASSWORD="PostgresPassword123!"
POSTGRES_DB="myapp"
POSTGRES_USER="appuser"
POSTGRES_USER_PASSWORD="AppUserPassword123!"
POSTGRES_PORT=5432
CONTAINER_NAME="postgresql"
POSTGRES_DATA_DIR="/opt/postgresql/data"
POSTGRES_CONFIG_DIR="/opt/postgresql/conf"

echo "开始安装PostgreSQL Docker容器..."

# 创建数据目录
echo "创建PostgreSQL数据和配置目录..."
sudo mkdir -p $POSTGRES_DATA_DIR
sudo mkdir -p $POSTGRES_CONFIG_DIR

# 创建PostgreSQL配置文件
echo "创建PostgreSQL配置文件..."
sudo tee $POSTGRES_CONFIG_DIR/postgresql.conf > /dev/null <<EOF
# PostgreSQL配置文件

# 连接设置
listen_addresses = '*'
port = 5432
max_connections = 200

# 内存设置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL设置
wal_buffers = 16MB
checkpoint_completion_target = 0.9
wal_level = replica
max_wal_senders = 3

# 查询规划器设置
random_page_cost = 1.1
effective_io_concurrency = 200

# 日志设置
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# 性能设置
autovacuum = on
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# 安全设置
ssl = off
password_encryption = scram-sha-256

# 区域设置
timezone = 'Asia/Shanghai'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'
EOF

# 创建pg_hba.conf配置文件
echo "创建PostgreSQL访问控制配置..."
sudo tee $POSTGRES_CONFIG_DIR/pg_hba.conf > /dev/null <<EOF
# PostgreSQL Client Authentication Configuration File

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     trust

# IPv4 local connections:
host    all             all             127.0.0.1/32            scram-sha-256
host    all             all             0.0.0.0/0               scram-sha-256

# IPv6 local connections:
host    all             all             ::1/128                 scram-sha-256

# Allow replication connections from localhost, by a user with the
# replication privilege.
local   replication     all                                     trust
host    replication     all             127.0.0.1/32            scram-sha-256
host    replication     all             ::1/128                 scram-sha-256
EOF

# 创建初始化SQL脚本
echo "创建数据库初始化脚本..."
sudo tee $POSTGRES_CONFIG_DIR/init.sql > /dev/null <<EOF
-- PostgreSQL初始化脚本

-- 创建应用用户
CREATE USER $POSTGRES_USER WITH PASSWORD '$POSTGRES_USER_PASSWORD';

-- 授予用户权限
ALTER USER $POSTGRES_USER CREATEDB;
GRANT ALL PRIVILEGES ON DATABASE $POSTGRES_DB TO $POSTGRES_USER;

-- 创建一些常用扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 显示创建结果
\du
\l
EOF

# 设置目录权限
sudo chown -R 999:999 $POSTGRES_DATA_DIR
sudo chown -R 999:999 $POSTGRES_CONFIG_DIR

# 拉取PostgreSQL镜像
echo "拉取PostgreSQL镜像..."
sudo docker pull postgres:15

# 停止并删除已存在的容器（如果有）
if sudo docker ps -a | grep -q $CONTAINER_NAME; then
    echo "停止并删除已存在的PostgreSQL容器..."
    sudo docker stop $CONTAINER_NAME || true
    sudo docker rm $CONTAINER_NAME || true
fi

# 创建并启动PostgreSQL容器
echo "创建并启动PostgreSQL容器..."
sudo docker run -d \
    --name $CONTAINER_NAME \
    --restart unless-stopped \
    -p $POSTGRES_PORT:5432 \
    -e POSTGRES_PASSWORD=$POSTGRES_PASSWORD \
    -e POSTGRES_DB=$POSTGRES_DB \
    -e POSTGRES_USER=postgres \
    -v $POSTGRES_DATA_DIR:/var/lib/postgresql/data \
    -v $POSTGRES_CONFIG_DIR/postgresql.conf:/etc/postgresql/postgresql.conf \
    -v $POSTGRES_CONFIG_DIR/pg_hba.conf:/etc/postgresql/pg_hba.conf \
    -v $POSTGRES_CONFIG_DIR/init.sql:/docker-entrypoint-initdb.d/init.sql \
    postgres:15 \
    -c 'config_file=/etc/postgresql/postgresql.conf' \
    -c 'hba_file=/etc/postgresql/pg_hba.conf'

# 等待PostgreSQL启动
echo "等待PostgreSQL启动..."
echo "PostgreSQL初始化可能需要1-2分钟..."

max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "检查PostgreSQL状态... ($attempt/$max_attempts)"
    
    if sudo docker exec $CONTAINER_NAME pg_isready -U postgres 2>/dev/null; then
        echo "PostgreSQL已成功启动！"
        break
    fi
    
    # 检查容器是否还在运行
    if ! sudo docker ps | grep -q $CONTAINER_NAME; then
        echo "容器停止运行，检查日志："
        sudo docker logs $CONTAINER_NAME
        exit 1
    fi
    
    sleep 10
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo "PostgreSQL启动超时，请检查容器日志："
    sudo docker logs $CONTAINER_NAME
    exit 1
fi

# 检查容器状态
echo "检查PostgreSQL容器状态..."
sudo docker ps | grep $CONTAINER_NAME

# 等待额外时间确保完全就绪
sleep 10

# 测试连接
echo "测试PostgreSQL连接..."
if sudo docker exec $CONTAINER_NAME psql -U postgres -d $POSTGRES_DB -c "SELECT version();" 2>/dev/null; then
    echo "✓ PostgreSQL连接测试成功"
else
    echo "✗ PostgreSQL连接测试失败"
    sudo docker logs $CONTAINER_NAME --tail 20
    exit 1
fi

# 显示数据库信息
echo "显示数据库列表..."
sudo docker exec $CONTAINER_NAME psql -U postgres -c "\l" 2>/dev/null

echo "显示用户列表..."
sudo docker exec $CONTAINER_NAME psql -U postgres -c "\du" 2>/dev/null

echo "PostgreSQL安装完成！"
echo "连接信息："
echo "  主机: localhost"
echo "  端口: $POSTGRES_PORT"
echo "  超级用户: postgres"
echo "  超级用户密码: $POSTGRES_PASSWORD"
echo "  数据库: $POSTGRES_DB"
echo "  应用用户: $POSTGRES_USER"
echo "  应用用户密码: $POSTGRES_USER_PASSWORD"
echo "  数据目录: $POSTGRES_DATA_DIR"
echo "  配置目录: $POSTGRES_CONFIG_DIR"
