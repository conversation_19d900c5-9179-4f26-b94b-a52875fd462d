#!/bin/bash

# MySQL完全重置脚本
# 作者: wujule
# 用于解决MySQL系统表损坏问题

set -e

# 配置变量
MYSQL_ROOT_PASSWORD="MyStrongPassword123!"
MYSQL_DATABASE="myapp"
MYSQL_USER="appuser"
MYSQL_PASSWORD="AppPassword123!"
CONTAINER_NAME="mysql8"
MYSQL_DATA_DIR="/opt/mysql/data"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${RED}MySQL完全重置脚本${NC}"
echo -e "${YELLOW}警告: 这将删除所有现有的MySQL数据！${NC}"
echo ""

# 确认操作
read -p "确定要完全重置MySQL吗？这将删除所有数据 (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo -e "${BLUE}开始MySQL完全重置...${NC}"

# 1. 停止并删除容器
echo -e "${YELLOW}停止并删除MySQL容器...${NC}"
sudo docker stop $CONTAINER_NAME 2>/dev/null || true
sudo docker rm $CONTAINER_NAME 2>/dev/null || true

# 2. 备份损坏的数据目录
if [ -d "$MYSQL_DATA_DIR" ]; then
    backup_dir="/opt/mysql/data_corrupted_backup_$(date +%Y%m%d_%H%M%S)"
    echo -e "${BLUE}备份损坏的数据目录到: $backup_dir${NC}"
    sudo mv $MYSQL_DATA_DIR $backup_dir
fi

# 3. 重新创建数据目录
echo -e "${BLUE}重新创建数据目录...${NC}"
sudo mkdir -p $MYSQL_DATA_DIR
sudo chown -R 999:999 $MYSQL_DATA_DIR

# 4. 创建新的MySQL容器
echo -e "${GREEN}创建新的MySQL容器...${NC}"
sudo docker run -d \
    --name $CONTAINER_NAME \
    --restart unless-stopped \
    -p 3306:3306 \
    -e MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD \
    -e MYSQL_DATABASE=$MYSQL_DATABASE \
    -e MYSQL_USER=$MYSQL_USER \
    -e MYSQL_PASSWORD=$MYSQL_PASSWORD \
    -v $MYSQL_DATA_DIR:/var/lib/mysql \
    mysql:8.0

echo "MySQL容器已创建，容器ID: $(sudo docker ps -q -f name=$CONTAINER_NAME)"

# 5. 等待MySQL完全初始化
echo -e "${BLUE}等待MySQL初始化完成...${NC}"
echo "首次初始化可能需要2-5分钟，请耐心等待..."

max_attempts=60  # 最多等待10分钟
attempt=1

while [ $attempt -le $max_attempts ]; do
    # 检查容器是否还在运行
    if ! sudo docker ps | grep -q $CONTAINER_NAME; then
        echo -e "${RED}容器停止运行，检查日志:${NC}"
        sudo docker logs $CONTAINER_NAME
        exit 1
    fi
    
    # 检查初始化是否完成
    if sudo docker logs $CONTAINER_NAME 2>/dev/null | grep -q "ready for connections"; then
        echo -e "${GREEN}MySQL初始化完成！${NC}"
        break
    fi
    
    # 检查是否有错误
    if sudo docker logs $CONTAINER_NAME 2>/dev/null | grep -q "ERROR.*Aborting"; then
        echo -e "${RED}MySQL初始化失败，查看日志:${NC}"
        sudo docker logs $CONTAINER_NAME
        exit 1
    fi
    
    printf "初始化进行中... (%d/%d)\r" $attempt $max_attempts
    sleep 10
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo -e "${RED}MySQL初始化超时${NC}"
    echo "查看日志:"
    sudo docker logs $CONTAINER_NAME
    exit 1
fi

# 6. 等待额外30秒确保完全就绪
echo "等待MySQL完全就绪..."
sleep 30

# 7. 测试连接
echo -e "${BLUE}测试MySQL连接...${NC}"
max_test_attempts=10
test_attempt=1

while [ $test_attempt -le $max_test_attempts ]; do
    if sudo docker exec $CONTAINER_NAME mysqladmin ping -uroot -p$MYSQL_ROOT_PASSWORD --silent 2>/dev/null; then
        echo -e "${GREEN}✓ MySQL连接成功！${NC}"
        break
    fi
    
    echo "等待MySQL就绪... ($test_attempt/$max_test_attempts)"
    sleep 5
    test_attempt=$((test_attempt + 1))
done

if [ $test_attempt -gt $max_test_attempts ]; then
    echo -e "${RED}MySQL连接测试失败${NC}"
    sudo docker logs $CONTAINER_NAME --tail 20
    exit 1
fi

# 8. 验证数据库和用户
echo -e "${BLUE}验证数据库和用户...${NC}"

# 显示版本
echo "MySQL版本:"
sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SELECT VERSION();" 2>/dev/null

# 显示数据库
echo -e "${BLUE}数据库列表:${NC}"
sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SHOW DATABASES;" 2>/dev/null

# 测试应用用户
echo -e "${BLUE}测试应用用户:${NC}"
if sudo docker exec $CONTAINER_NAME mysql -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "SELECT 'User connection successful!' as message;" 2>/dev/null; then
    echo -e "${GREEN}✓ 应用用户连接成功${NC}"
else
    echo -e "${YELLOW}⚠ 应用用户连接失败，但root用户正常${NC}"
fi

echo ""
echo -e "${GREEN}=== MySQL重置完成 ===${NC}"
echo ""
echo -e "${BLUE}连接信息:${NC}"
echo "主机: localhost"
echo "端口: 3306"
echo "Root密码: $MYSQL_ROOT_PASSWORD"
echo "数据库: $MYSQL_DATABASE"
echo "用户: $MYSQL_USER"
echo "用户密码: $MYSQL_PASSWORD"
echo ""
echo -e "${GREEN}MySQL现在应该正常工作了！${NC}"
