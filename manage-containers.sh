#!/bin/bash

# Docker容器管理脚本
# 作者: AI Assistant
# 日期: $(date)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Docker容器管理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  status      显示所有容器状态"
    echo "  start       启动所有容器"
    echo "  stop        停止所有容器"
    echo "  restart     重启所有容器"
    echo "  logs        显示容器日志"
    echo "  backup      备份数据"
    echo "  cleanup     清理未使用的镜像和容器"
    echo "  monitor     监控容器资源使用"
    echo "  wait-mysql  等待MySQL完全启动"
    echo "  test-mysql  测试MySQL连接"
    echo "  help        显示此帮助信息"
    echo ""
}

# 显示容器状态
show_status() {
    echo -e "${BLUE}=== 容器状态 ===${NC}"
    sudo docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""

    # 检查MySQL连接状态
    if sudo docker ps | grep -q mysql8; then
        echo -e "${BLUE}=== MySQL连接测试 ===${NC}"
        if sudo docker exec mysql8 mysqladmin ping -uroot -pMyStrongPassword123! --silent 2>/dev/null; then
            echo -e "${GREEN}✓ MySQL连接正常${NC}"
            mysql_version=$(sudo docker exec mysql8 mysql -uroot -pMyStrongPassword123! -e "SELECT VERSION();" -s -N 2>/dev/null)
            echo "MySQL版本: $mysql_version"
        else
            echo -e "${YELLOW}⚠ MySQL正在启动或连接失败${NC}"
            echo "提示: MySQL首次启动可能需要几分钟初始化"
        fi
        echo ""
    fi

    # 检查Redis连接状态
    if sudo docker ps | grep -q redis; then
        echo -e "${BLUE}=== Redis连接测试 ===${NC}"
        if sudo docker exec redis redis-cli -a RedisPassword123! ping 2>/dev/null | grep -q PONG; then
            echo -e "${GREEN}✓ Redis连接正常${NC}"
            redis_version=$(sudo docker exec redis redis-cli -a RedisPassword123! info server 2>/dev/null | grep redis_version | cut -d: -f2 | tr -d '\r')
            echo "Redis版本: $redis_version"
        else
            echo -e "${YELLOW}⚠ Redis连接失败${NC}"
        fi
        echo ""
    fi

    # 检查PostgreSQL连接状态
    if sudo docker ps | grep -q postgresql; then
        echo -e "${BLUE}=== PostgreSQL连接测试 ===${NC}"
        if sudo docker exec postgresql pg_isready -U postgres 2>/dev/null; then
            echo -e "${GREEN}✓ PostgreSQL连接正常${NC}"
            postgres_version=$(sudo docker exec postgresql psql -U postgres -t -c "SELECT version();" 2>/dev/null | head -1 | sed 's/^ *//')
            echo "PostgreSQL版本: $postgres_version"
        else
            echo -e "${YELLOW}⚠ PostgreSQL连接失败${NC}"
        fi
        echo ""
    fi

    echo -e "${BLUE}=== 系统资源使用 ===${NC}"
    sudo docker system df
    echo ""
}

# 启动所有容器
start_containers() {
    echo -e "${GREEN}启动所有容器...${NC}"
    
    containers=("mysql8" "redis" "postgresql")

    for container in "${containers[@]}"; do
        if sudo docker ps -a | grep -q $container; then
            echo -e "启动容器: ${YELLOW}$container${NC}"
            sudo docker start $container
        else
            echo -e "${RED}容器 $container 不存在${NC}"
        fi
    done
    
    echo -e "${GREEN}容器启动完成${NC}"
}

# 停止所有容器
stop_containers() {
    echo -e "${YELLOW}停止所有容器...${NC}"
    
    containers=("mysql8" "redis" "postgresql")

    for container in "${containers[@]}"; do
        if sudo docker ps | grep -q $container; then
            echo -e "停止容器: ${YELLOW}$container${NC}"
            sudo docker stop $container
        else
            echo -e "${RED}容器 $container 未运行${NC}"
        fi
    done
    
    echo -e "${YELLOW}容器停止完成${NC}"
}

# 重启所有容器
restart_containers() {
    echo -e "${BLUE}重启所有容器...${NC}"
    stop_containers
    sleep 5
    start_containers
}

# 显示容器日志
show_logs() {
    echo -e "${BLUE}=== MySQL日志 ===${NC}"
    sudo docker logs --tail 20 mysql8 2>/dev/null || echo "MySQL容器未运行"
    echo ""
    
    echo -e "${BLUE}=== Redis日志 ===${NC}"
    sudo docker logs --tail 20 redis 2>/dev/null || echo "Redis容器未运行"
    echo ""
}

# 备份数据
backup_data() {
    BACKUP_DIR="/opt/backups/$(date +%Y%m%d_%H%M%S)"
    echo -e "${GREEN}创建备份目录: $BACKUP_DIR${NC}"
    sudo mkdir -p $BACKUP_DIR
    
    # 备份MySQL数据
    if sudo docker ps | grep -q mysql8; then
        echo -e "${GREEN}备份MySQL数据...${NC}"
        sudo docker exec mysql8 mysqldump -uroot -pMyStrongPassword123! --all-databases > $BACKUP_DIR/mysql_backup.sql
        sudo cp -r /opt/mysql/data $BACKUP_DIR/mysql_data_backup
    fi
    
    # 备份Redis数据
    if sudo docker ps | grep -q redis; then
        echo -e "${GREEN}备份Redis数据...${NC}"
        sudo docker exec redis redis-cli -a RedisPassword123! BGSAVE
        sleep 5
        sudo cp -r /opt/redis/data $BACKUP_DIR/redis_data_backup
    fi

    # 备份PostgreSQL数据
    if sudo docker ps | grep -q postgresql; then
        echo -e "${GREEN}备份PostgreSQL数据...${NC}"
        sudo docker exec postgresql pg_dumpall -U postgres > $BACKUP_DIR/postgresql_backup.sql
        sudo cp -r /opt/postgresql/data $BACKUP_DIR/postgresql_data_backup
    fi

    echo -e "${GREEN}备份完成: $BACKUP_DIR${NC}"
}

# 清理系统
cleanup_system() {
    echo -e "${YELLOW}清理Docker系统...${NC}"
    
    # 清理未使用的容器
    echo "清理停止的容器..."
    sudo docker container prune -f
    
    # 清理未使用的镜像
    echo "清理未使用的镜像..."
    sudo docker image prune -f
    
    # 清理未使用的网络
    echo "清理未使用的网络..."
    sudo docker network prune -f
    
    # 清理未使用的卷
    echo "清理未使用的卷..."
    sudo docker volume prune -f
    
    echo -e "${GREEN}清理完成${NC}"
}

# 等待MySQL完全启动
wait_for_mysql() {
    echo -e "${BLUE}等待MySQL完全启动...${NC}"

    if ! sudo docker ps | grep -q mysql8; then
        echo -e "${RED}MySQL容器未运行${NC}"
        return 1
    fi

    local max_attempts=60  # 最多等待5分钟
    local attempt=1

    echo "这可能需要几分钟时间，特别是首次启动时..."

    while [ $attempt -le $max_attempts ]; do
        printf "尝试连接MySQL... (%d/%d)\r" $attempt $max_attempts

        if sudo docker exec mysql8 mysqladmin ping -uroot -pMyStrongPassword123! --silent 2>/dev/null; then
            echo -e "\n${GREEN}MySQL已成功启动并准备接受连接！${NC}"
            return 0
        fi

        # 检查容器是否还在运行
        if ! sudo docker ps | grep -q mysql8; then
            echo -e "\n${RED}MySQL容器已停止运行${NC}"
            return 1
        fi

        sleep 5
        attempt=$((attempt + 1))
    done

    echo -e "\n${RED}MySQL启动超时${NC}"
    echo "请检查容器日志: sudo docker logs mysql8"
    return 1
}

# 测试MySQL连接
test_mysql_connection() {
    echo -e "${BLUE}测试MySQL连接...${NC}"

    if ! sudo docker ps | grep -q mysql8; then
        echo -e "${RED}MySQL容器未运行${NC}"
        return 1
    fi

    # 测试基本连接
    echo "测试root用户连接..."
    if sudo docker exec mysql8 mysql -uroot -pMyStrongPassword123! -e "SELECT 'MySQL连接成功!' as message;" 2>/dev/null; then
        echo -e "${GREEN}✓ Root用户连接成功${NC}"
    else
        echo -e "${RED}✗ Root用户连接失败${NC}"
        echo "请等待MySQL完全启动，或运行: ./manage-containers.sh wait-mysql"
        return 1
    fi

    # 显示版本信息
    echo -e "${BLUE}MySQL版本信息:${NC}"
    sudo docker exec mysql8 mysql -uroot -pMyStrongPassword123! -e "SELECT VERSION();" 2>/dev/null

    # 显示数据库列表
    echo -e "${BLUE}数据库列表:${NC}"
    sudo docker exec mysql8 mysql -uroot -pMyStrongPassword123! -e "SHOW DATABASES;" 2>/dev/null

    # 测试应用用户连接
    echo -e "${BLUE}测试应用用户连接:${NC}"
    if sudo docker exec mysql8 mysql -uappuser -pAppPassword123! myapp -e "SELECT 'appuser连接成功!' as message;" 2>/dev/null; then
        echo -e "${GREEN}✓ 应用用户连接成功${NC}"
    else
        echo -e "${YELLOW}⚠ 应用用户连接失败（可能需要手动创建用户）${NC}"
    fi

    echo -e "${GREEN}MySQL连接测试完成${NC}"
}

# 监控容器资源
monitor_containers() {
    echo -e "${BLUE}=== 容器资源监控 ===${NC}"
    echo "按 Ctrl+C 退出监控"
    echo ""
    
    while true; do
        clear
        echo -e "${BLUE}=== $(date) ===${NC}"
        sudo docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
        echo ""
        echo -e "${YELLOW}按 Ctrl+C 退出${NC}"
        sleep 5
    done
}

# 主程序
case "$1" in
    status)
        show_status
        ;;
    start)
        start_containers
        ;;
    stop)
        stop_containers
        ;;
    restart)
        restart_containers
        ;;
    logs)
        show_logs
        ;;
    backup)
        backup_data
        ;;
    cleanup)
        cleanup_system
        ;;
    monitor)
        monitor_containers
        ;;
    wait-mysql)
        wait_for_mysql
        ;;
    test-mysql)
        test_mysql_connection
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}未知选项: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
