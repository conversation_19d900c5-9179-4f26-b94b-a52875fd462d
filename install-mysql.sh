#!/bin/bash

# MySQL 8.0 Docker安装脚本
# 作者: AI Assistant
# 日期: $(date)

set -e

# 配置变量
MYSQL_ROOT_PASSWORD="MyStrongPassword123!"
MYSQL_DATABASE="myapp"
MYSQL_USER="appuser"
MYSQL_PASSWORD="AppPassword123!"
MYSQL_PORT=3306
CONTAINER_NAME="mysql8"
MYSQL_DATA_DIR="/opt/mysql/data"
MYSQL_CONFIG_DIR="/opt/mysql/conf"

echo "开始安装MySQL 8.0 Docker容器..."

# 创建数据目录
echo "创建MySQL数据和配置目录..."
sudo mkdir -p $MYSQL_DATA_DIR
sudo mkdir -p $MYSQL_CONFIG_DIR

# 创建MySQL配置文件
echo "创建MySQL配置文件..."
sudo tee $MYSQL_CONFIG_DIR/my.cnf > /dev/null <<EOF
[mysqld]
# 基本设置
port = 3306
bind-address = 0.0.0.0
server-id = 1

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 连接设置
max_connections = 200
max_connect_errors = 1000

# 缓存设置
innodb_buffer_pool_size = 256M
query_cache_size = 64M
query_cache_type = 1

# 日志设置
log-error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 安全设置
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
EOF

# 设置目录权限
sudo chown -R 999:999 $MYSQL_DATA_DIR
sudo chown -R 999:999 $MYSQL_CONFIG_DIR

# 拉取MySQL 8.0镜像
echo "拉取MySQL 8.0镜像..."
sudo docker pull mysql:8.0

# 停止并删除已存在的容器（如果有）
if sudo docker ps -a | grep -q $CONTAINER_NAME; then
    echo "停止并删除已存在的MySQL容器..."
    sudo docker stop $CONTAINER_NAME || true
    sudo docker rm $CONTAINER_NAME || true
fi

# 创建并启动MySQL容器
echo "创建并启动MySQL容器..."
sudo docker run -d \
    --name $CONTAINER_NAME \
    --restart unless-stopped \
    -p $MYSQL_PORT:3306 \
    -e MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD \
    -e MYSQL_DATABASE=$MYSQL_DATABASE \
    -e MYSQL_USER=$MYSQL_USER \
    -e MYSQL_PASSWORD=$MYSQL_PASSWORD \
    -v $MYSQL_DATA_DIR:/var/lib/mysql \
    -v $MYSQL_CONFIG_DIR/my.cnf:/etc/mysql/conf.d/my.cnf \
    mysql:8.0

# 等待MySQL启动
echo "等待MySQL启动..."
echo "正在初始化MySQL数据库，这可能需要几分钟时间..."

# 智能等待MySQL完全启动
wait_for_mysql() {
    local max_attempts=60  # 最多等待5分钟
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo "尝试连接MySQL... ($attempt/$max_attempts)"

        if sudo docker exec $CONTAINER_NAME mysqladmin ping -uroot -p$MYSQL_ROOT_PASSWORD --silent 2>/dev/null; then
            echo "MySQL已成功启动！"
            return 0
        fi

        if [ $attempt -eq 1 ]; then
            echo "MySQL正在初始化，请耐心等待..."
        fi

        sleep 5
        attempt=$((attempt + 1))
    done

    echo "MySQL启动超时，请检查容器日志："
    sudo docker logs $CONTAINER_NAME
    return 1
}

# 检查容器状态
echo "检查MySQL容器状态..."
sudo docker ps | grep $CONTAINER_NAME

# 等待MySQL完全启动
if wait_for_mysql; then
    # 测试连接
    echo "测试MySQL连接..."
    sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SELECT VERSION();"

    # 显示数据库列表
    echo "显示数据库列表..."
    sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SHOW DATABASES;"
else
    echo "MySQL启动失败，请检查错误信息"
    exit 1
fi

echo "MySQL 8.0安装完成！"
echo "连接信息："
echo "  主机: localhost"
echo "  端口: $MYSQL_PORT"
echo "  Root密码: $MYSQL_ROOT_PASSWORD"
echo "  数据库: $MYSQL_DATABASE"
echo "  用户: $MYSQL_USER"
echo "  用户密码: $MYSQL_PASSWORD"
echo "  数据目录: $MYSQL_DATA_DIR"
echo "  配置文件: $MYSQL_CONFIG_DIR/my.cnf"
