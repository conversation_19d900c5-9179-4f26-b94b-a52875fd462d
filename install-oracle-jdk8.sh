#!/bin/bash

# Oracle JDK 1.8 安装脚本 for CentOS 7.9
# 作者: wujule
# 注意: 需要手动下载Oracle JDK安装包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
JDK_VERSION="1.8.0_391"
JDK_BUILD="b13"
JDK_PACKAGE="jdk-8u391-linux-x64.tar.gz"
JDK_DIR="jdk1.8.0_391"
INSTALL_DIR="/usr/local/java"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}Oracle JDK 1.8 安装脚本${NC}"
echo ""

# 检查JDK安装包
check_jdk_package() {
    log_info "检查JDK安装包..."
    
    if [[ ! -f "$JDK_PACKAGE" ]]; then
        log_error "未找到JDK安装包: $JDK_PACKAGE"
        echo ""
        echo -e "${YELLOW}请按以下步骤下载Oracle JDK:${NC}"
        echo "1. 访问Oracle官网: https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html"
        echo "2. 下载 jdk-8u391-linux-x64.tar.gz (或最新版本)"
        echo "3. 将下载的文件放在当前目录"
        echo "4. 重新运行此脚本"
        echo ""
        echo -e "${BLUE}或者使用wget下载 (需要Oracle账号):${NC}"
        echo "wget --no-cookies --no-check-certificate --header \"Cookie: gpw_e24=http%3A%2F%2Fwww.oracle.com%2F; oraclelicense=accept-securebackup-cookie\" \"https://download.oracle.com/otn-pub/java/jdk/8u391-b13/4f5417147a92418ea8b615e228bb6935/jdk-8u391-linux-x64.tar.gz\""
        exit 1
    fi
    
    log_success "找到JDK安装包: $JDK_PACKAGE"
}

# 创建安装目录
create_install_directory() {
    log_info "创建安装目录..."
    sudo mkdir -p $INSTALL_DIR
    log_success "安装目录创建完成: $INSTALL_DIR"
}

# 解压安装JDK
extract_jdk() {
    log_info "解压JDK安装包..."
    
    # 解压到临时目录
    tar -xzf $JDK_PACKAGE
    
    # 移动到安装目录
    sudo mv $JDK_DIR $INSTALL_DIR/
    
    # 创建符号链接
    sudo ln -sfn $INSTALL_DIR/$JDK_DIR $INSTALL_DIR/current
    
    log_success "JDK解压安装完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置Java环境变量..."
    
    # 创建环境变量配置文件
    sudo tee /etc/profile.d/oracle-java.sh > /dev/null <<EOF
#!/bin/bash
# Oracle Java Environment Variables
export JAVA_HOME=$INSTALL_DIR/current
export JRE_HOME=\$JAVA_HOME/jre
export PATH=\$JAVA_HOME/bin:\$JRE_HOME/bin:\$PATH
export CLASSPATH=.:\$JAVA_HOME/lib:\$JRE_HOME/lib
EOF
    
    # 设置执行权限
    sudo chmod +x /etc/profile.d/oracle-java.sh
    
    # 加载环境变量
    source /etc/profile.d/oracle-java.sh
    
    log_success "环境变量配置完成"
}

# 设置默认Java版本
set_default_java() {
    log_info "设置默认Java版本..."
    
    # 使用alternatives设置默认java
    sudo alternatives --install /usr/bin/java java $INSTALL_DIR/current/bin/java 2
    sudo alternatives --install /usr/bin/javac javac $INSTALL_DIR/current/bin/javac 2
    sudo alternatives --install /usr/bin/jar jar $INSTALL_DIR/current/bin/jar 2
    
    # 设置为默认
    sudo alternatives --set java $INSTALL_DIR/current/bin/java
    sudo alternatives --set javac $INSTALL_DIR/current/bin/javac
    sudo alternatives --set jar $INSTALL_DIR/current/bin/jar
    
    log_success "默认Java版本设置完成"
}

# 验证安装
verify_installation() {
    log_info "验证Java安装..."
    
    # 重新加载环境变量
    source /etc/profile.d/oracle-java.sh
    
    echo -e "${BLUE}Java版本信息:${NC}"
    java -version
    echo ""
    
    echo -e "${BLUE}Java编译器版本:${NC}"
    javac -version
    echo ""
    
    echo -e "${BLUE}环境变量:${NC}"
    echo "JAVA_HOME: $JAVA_HOME"
    echo "JRE_HOME: $JRE_HOME"
    echo ""
    
    # 测试Java程序
    log_info "创建测试Java程序..."
    cat > /tmp/OracleJavaTest.java << 'EOF'
public class OracleJavaTest {
    public static void main(String[] args) {
        System.out.println("Oracle Java Test");
        System.out.println("Java version: " + System.getProperty("java.version"));
        System.out.println("Java vendor: " + System.getProperty("java.vendor"));
        System.out.println("Java home: " + System.getProperty("java.home"));
    }
}
EOF
    
    # 编译和运行测试程序
    log_info "编译测试程序..."
    cd /tmp
    javac OracleJavaTest.java
    
    log_info "运行测试程序..."
    java OracleJavaTest
    
    # 清理测试文件
    rm -f /tmp/OracleJavaTest.java /tmp/OracleJavaTest.class
    
    log_success "Oracle Java安装验证成功！"
}

# 显示安装信息
show_installation_info() {
    echo ""
    echo -e "${GREEN}==================== Oracle JDK 1.8 安装完成 ====================${NC}"
    echo ""
    echo -e "${BLUE}安装信息:${NC}"
    echo "Java版本: $(java -version 2>&1 | head -n 1)"
    echo "安装路径: $INSTALL_DIR/$JDK_DIR"
    echo "符号链接: $INSTALL_DIR/current"
    echo "配置文件: /etc/profile.d/oracle-java.sh"
    echo ""
    echo -e "${BLUE}常用命令:${NC}"
    echo "查看Java版本: java -version"
    echo "查看编译器版本: javac -version"
    echo "查看Java路径: echo \$JAVA_HOME"
    echo "切换Java版本: sudo alternatives --config java"
    echo ""
    echo -e "${BLUE}环境变量:${NC}"
    echo "JAVA_HOME: $JAVA_HOME"
    echo "JRE_HOME: $JRE_HOME"
    echo "PATH: 已添加Java bin目录"
    echo "CLASSPATH: 已配置基本类路径"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo "1. 环境变量已全局配置，新终端会自动生效"
    echo "2. 当前终端需要运行 'source /etc/profile.d/oracle-java.sh' 或重新登录"
    echo "3. Oracle JDK需要商业许可证用于生产环境"
    echo ""
    echo -e "${GREEN}=================================================${NC}"
}

# 主安装流程
main() {
    echo -e "${BLUE}开始安装Oracle JDK 1.8...${NC}"
    echo ""
    
    # 检查JDK包和权限
    check_jdk_package
    
    # 确认安装
    echo -e "${YELLOW}即将安装:${NC}"
    echo "- Oracle JDK 1.8"
    echo "- 安装路径: $INSTALL_DIR"
    echo "- 环境变量配置"
    echo ""
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi
    
    # 执行安装
    create_install_directory
    extract_jdk
    configure_environment
    set_default_java
    verify_installation
    show_installation_info
}

# 运行主程序
main "$@"
