#!/bin/bash

# Java环境管理脚本
# 作者: wujule

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Java环境管理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  status      显示当前Java环境状态"
    echo "  versions    显示所有可用的Java版本"
    echo "  switch      切换Java版本"
    echo "  test        测试Java环境"
    echo "  info        显示详细的Java信息"
    echo "  cleanup     清理Java临时文件"
    echo "  help        显示此帮助信息"
    echo ""
}

# 显示Java状态
show_status() {
    echo -e "${BLUE}=== Java环境状态 ===${NC}"
    
    if command -v java &> /dev/null; then
        echo -e "${GREEN}✓ Java已安装${NC}"
        echo "当前版本:"
        java -version
        echo ""
        
        if [[ -n "$JAVA_HOME" ]]; then
            echo -e "${GREEN}✓ JAVA_HOME已设置${NC}"
            echo "JAVA_HOME: $JAVA_HOME"
        else
            echo -e "${YELLOW}⚠ JAVA_HOME未设置${NC}"
        fi
        
        echo ""
        echo "Java可执行文件路径:"
        which java
        
        if command -v javac &> /dev/null; then
            echo -e "${GREEN}✓ Java编译器可用${NC}"
            echo "编译器路径:"
            which javac
        else
            echo -e "${YELLOW}⚠ Java编译器不可用${NC}"
        fi
    else
        echo -e "${RED}✗ Java未安装${NC}"
    fi
    echo ""
}

# 显示所有Java版本
show_versions() {
    echo -e "${BLUE}=== 可用的Java版本 ===${NC}"
    
    if command -v alternatives &> /dev/null; then
        echo "通过alternatives管理的Java版本:"
        sudo alternatives --display java 2>/dev/null || echo "未找到alternatives配置"
        echo ""
    fi
    
    echo "系统中的Java安装:"
    find /usr/lib/jvm /usr/local/java -name "java" -type f 2>/dev/null | head -10 || echo "未找到其他Java安装"
    echo ""
}

# 切换Java版本
switch_java() {
    echo -e "${BLUE}=== 切换Java版本 ===${NC}"
    
    if command -v alternatives &> /dev/null; then
        echo "可用的Java版本:"
        sudo alternatives --config java
    else
        echo -e "${YELLOW}alternatives命令不可用，无法切换版本${NC}"
        echo "请手动修改环境变量或重新安装Java"
    fi
}

# 测试Java环境
test_java() {
    echo -e "${BLUE}=== Java环境测试 ===${NC}"
    
    if ! command -v java &> /dev/null; then
        echo -e "${RED}✗ Java未安装，无法进行测试${NC}"
        return 1
    fi
    
    # 创建测试程序
    cat > /tmp/JavaEnvTest.java << 'EOF'
import java.util.Properties;

public class JavaEnvTest {
    public static void main(String[] args) {
        System.out.println("=== Java环境测试 ===");
        
        Properties props = System.getProperties();
        
        System.out.println("Java版本: " + props.getProperty("java.version"));
        System.out.println("Java供应商: " + props.getProperty("java.vendor"));
        System.out.println("Java主目录: " + props.getProperty("java.home"));
        System.out.println("操作系统: " + props.getProperty("os.name") + " " + props.getProperty("os.version"));
        System.out.println("用户目录: " + props.getProperty("user.home"));
        System.out.println("工作目录: " + props.getProperty("user.dir"));
        
        System.out.println("\n=== 内存信息 ===");
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        
        System.out.println("最大内存: " + (maxMemory / 1024 / 1024) + " MB");
        System.out.println("总内存: " + (totalMemory / 1024 / 1024) + " MB");
        System.out.println("空闲内存: " + (freeMemory / 1024 / 1024) + " MB");
        
        System.out.println("\n=== 类路径 ===");
        String classpath = System.getProperty("java.class.path");
        String[] paths = classpath.split(":");
        for (String path : paths) {
            System.out.println("  " + path);
        }
        
        System.out.println("\n✓ Java环境测试完成");
    }
}
EOF
    
    # 编译测试程序
    echo "编译测试程序..."
    cd /tmp
    if javac JavaEnvTest.java 2>/dev/null; then
        echo -e "${GREEN}✓ 编译成功${NC}"
    else
        echo -e "${RED}✗ 编译失败，检查javac是否可用${NC}"
        rm -f /tmp/JavaEnvTest.java
        return 1
    fi
    
    # 运行测试程序
    echo "运行测试程序..."
    echo ""
    java JavaEnvTest
    
    # 清理测试文件
    rm -f /tmp/JavaEnvTest.java /tmp/JavaEnvTest.class
    
    echo ""
    echo -e "${GREEN}✓ Java环境测试完成${NC}"
}

# 显示详细信息
show_info() {
    echo -e "${BLUE}=== 详细Java信息 ===${NC}"
    
    if ! command -v java &> /dev/null; then
        echo -e "${RED}✗ Java未安装${NC}"
        return 1
    fi
    
    echo -e "${BLUE}版本信息:${NC}"
    java -version
    echo ""
    
    if command -v javac &> /dev/null; then
        echo -e "${BLUE}编译器版本:${NC}"
        javac -version
        echo ""
    fi
    
    echo -e "${BLUE}环境变量:${NC}"
    echo "JAVA_HOME: ${JAVA_HOME:-未设置}"
    echo "JRE_HOME: ${JRE_HOME:-未设置}"
    echo "CLASSPATH: ${CLASSPATH:-未设置}"
    echo ""
    
    echo -e "${BLUE}可执行文件路径:${NC}"
    echo "java: $(which java 2>/dev/null || echo '未找到')"
    echo "javac: $(which javac 2>/dev/null || echo '未找到')"
    echo "jar: $(which jar 2>/dev/null || echo '未找到')"
    echo ""
    
    echo -e "${BLUE}Java系统属性:${NC}"
    java -XshowSettings:properties -version 2>&1 | grep -E "(java\.|os\.|user\.)" | head -10
    echo ""
}

# 清理Java临时文件
cleanup_java() {
    echo -e "${BLUE}=== 清理Java临时文件 ===${NC}"
    
    # 清理用户临时目录中的Java文件
    if [[ -d "/tmp" ]]; then
        echo "清理/tmp目录中的Java临时文件..."
        find /tmp -name "*.class" -o -name "*.jar" -o -name "hsperfdata_*" -type f -mtime +7 2>/dev/null | wc -l | xargs echo "找到文件数量:"
        find /tmp -name "*.class" -o -name "*.jar" -o -name "hsperfdata_*" -type f -mtime +7 -delete 2>/dev/null || true
    fi
    
    # 清理Java缓存目录
    if [[ -d "$HOME/.java" ]]; then
        echo "清理Java缓存目录..."
        find "$HOME/.java" -name "*.tmp" -type f -mtime +7 -delete 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✓ Java临时文件清理完成${NC}"
}

# 主程序
case "$1" in
    status)
        show_status
        ;;
    versions)
        show_versions
        ;;
    switch)
        switch_java
        ;;
    test)
        test_java
        ;;
    info)
        show_info
        ;;
    cleanup)
        cleanup_java
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}未知选项: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
