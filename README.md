# CentOS 7.9 Docker + MySQL 8.0 + Redis + PostgreSQL 安装指南

本项目提供了在CentOS 7.9环境中使用Docker安装MySQL 8.0、Redis和PostgreSQL的完整解决方案。

## 📋 系统要求

- CentOS 7.9
- 至少2GB内存
- 至少10GB可用磁盘空间
- Root权限或sudo权限

## 🚀 快速安装

### 一键安装（推荐）

```bash
# 给脚本执行权限
chmod +x install-all.sh

# 运行一键安装脚本
./install-all.sh
```

### 分步安装

如果您希望分步安装，可以按以下顺序执行：

```bash
# 1. 安装Docker
chmod +x install-docker.sh
./install-docker.sh

# 2. 安装MySQL 8.0
chmod +x install-mysql.sh
./install-mysql.sh

# 3. 安装Redis
chmod +x install-redis.sh
./install-redis.sh
```

## 📁 文件说明

| 文件名 | 说明 |
|--------|------|
| `install-all.sh` | 一键安装脚本 |
| `install-docker.sh` | Docker安装脚本 |
| `install-mysql.sh` | MySQL 8.0安装脚本 |
| `install-redis.sh` | Redis安装脚本 |
| `manage-containers.sh` | 容器管理脚本 |
| `README.md` | 说明文档 |

## 🔧 默认配置

### MySQL 8.0
- **端口**: 3306
- **Root密码**: MyStrongPassword123!
- **数据库**: myapp
- **用户**: appuser
- **用户密码**: AppPassword123!
- **数据目录**: /opt/mysql/data
- **配置文件**: /opt/mysql/conf/my.cnf

### Redis
- **端口**: 6379
- **密码**: RedisPassword123!
- **数据目录**: /opt/redis/data
- **配置文件**: /opt/redis/conf/redis.conf

### PostgreSQL 15
- **端口**: 5432
- **超级用户**: postgres
- **超级用户密码**: PostgresPassword123!
- **数据库**: myapp
- **应用用户**: appuser
- **应用用户密码**: AppUserPassword123!
- **数据目录**: /opt/postgresql/data
- **配置目录**: /opt/postgresql/conf

## 🛠️ 容器管理

使用 `manage-containers.sh` 脚本管理容器：

```bash
# 查看容器状态
./manage-containers.sh status

# 启动所有容器
./manage-containers.sh start

# 停止所有容器
./manage-containers.sh stop

# 重启所有容器
./manage-containers.sh restart

# 查看容器日志
./manage-containers.sh logs

# 备份数据
./manage-containers.sh backup

# 清理系统
./manage-containers.sh cleanup

# 监控资源使用
./manage-containers.sh monitor

# 显示帮助
./manage-containers.sh help
```

## 🔐 安全建议

1. **修改默认密码**
   ```bash
   # 修改MySQL root密码
   sudo docker exec -it mysql8 mysql -uroot -p
   ALTER USER 'root'@'%' IDENTIFIED BY '新密码';
   
   # 修改Redis密码
   # 编辑 /opt/redis/conf/redis.conf 文件中的 requirepass 配置
   ```

2. **配置防火墙**
   ```bash
   # 如果使用iptables
   sudo iptables -A INPUT -p tcp --dport 3306 -j ACCEPT
   sudo iptables -A INPUT -p tcp --dport 6379 -j ACCEPT
   
   # 如果使用firewalld
   sudo firewall-cmd --permanent --add-port=3306/tcp
   sudo firewall-cmd --permanent --add-port=6379/tcp
   sudo firewall-cmd --reload
   ```

3. **限制网络访问**
   - 考虑只允许特定IP访问数据库
   - 使用VPN或内网访问

## 📊 连接测试

### MySQL连接测试
```bash
# 使用Docker客户端连接
sudo docker exec -it mysql8 mysql -uroot -pMyStrongPassword123!

# 使用外部客户端连接
mysql -h localhost -P 3306 -u appuser -pAppPassword123! myapp
```

### Redis连接测试
```bash
# 使用Docker客户端连接
sudo docker exec -it redis redis-cli -a RedisPassword123!

# 使用外部客户端连接
redis-cli -h localhost -p 6379 -a RedisPassword123!
```

### PostgreSQL连接测试
```bash
# 使用Docker客户端连接
sudo docker exec -it postgresql psql -U postgres -d myapp

# 使用外部客户端连接
psql -h localhost -p 5432 -U appuser -d myapp
```

## 🔄 数据备份与恢复

### 自动备份
```bash
# 创建备份
./manage-containers.sh backup
```

### 手动备份
```bash
# MySQL备份
sudo docker exec mysql8 mysqldump -uroot -pMyStrongPassword123! --all-databases > mysql_backup.sql

# Redis备份
sudo docker exec redis redis-cli -a RedisPassword123! BGSAVE
```

### 数据恢复
```bash
# MySQL恢复
sudo docker exec -i mysql8 mysql -uroot -pMyStrongPassword123! < mysql_backup.sql

# Redis恢复（复制dump.rdb文件到数据目录后重启容器）
sudo docker restart redis
```

## 🐛 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看容器日志
   sudo docker logs mysql8
   sudo docker logs redis
   ```

2. **端口被占用**
   ```bash
   # 检查端口使用情况
   sudo netstat -tlnp | grep :3306
   sudo netstat -tlnp | grep :6379
   ```

3. **权限问题**
   ```bash
   # 检查数据目录权限
   ls -la /opt/mysql/data
   ls -la /opt/redis/data
   ```

4. **内存不足**
   ```bash
   # 检查系统资源
   free -h
   df -h
   ```

### 重新安装

如果需要重新安装：

```bash
# 停止并删除容器
sudo docker stop mysql8 redis
sudo docker rm mysql8 redis

# 删除数据目录（注意：这会删除所有数据）
sudo rm -rf /opt/mysql /opt/redis

# 重新运行安装脚本
./install-all.sh
```

## 📞 支持

如果遇到问题，请检查：
1. 系统日志：`sudo journalctl -u docker`
2. 容器日志：`sudo docker logs <容器名>`
3. 系统资源：`free -h` 和 `df -h`

## 📝 更新日志

- v1.0: 初始版本，支持CentOS 7.9 + Docker + MySQL 8.0 + Redis
