#!/bin/bash

# CentOS 7.9 一键安装Docker + MySQL 8.0 + Redis脚本
# 作者: AI Assistant
# 日期: $(date)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户或有sudo权限
check_privileges() {
    if [[ $EUID -eq 0 ]]; then
        log_info "以root用户运行"
    elif sudo -n true 2>/dev/null; then
        log_info "检测到sudo权限"
    else
        log_error "需要root权限或sudo权限来运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [[ -f /etc/redhat-release ]]; then
        local version=$(cat /etc/redhat-release)
        log_info "系统版本: $version"
        
        if [[ $version == *"CentOS Linux release 7"* ]]; then
            log_success "系统版本检查通过"
        else
            log_warning "此脚本专为CentOS 7.9设计，当前系统可能不兼容"
            read -p "是否继续安装？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    else
        log_error "无法检测系统版本"
        exit 1
    fi
}

# 安装Docker
install_docker() {
    log_info "开始安装Docker..."
    
    if command -v docker &> /dev/null; then
        log_warning "Docker已安装，跳过安装步骤"
        return
    fi
    
    chmod +x install-docker.sh
    ./install-docker.sh
    
    log_success "Docker安装完成"
}

# 安装MySQL
install_mysql() {
    log_info "开始安装MySQL 8.0..."
    
    chmod +x install-mysql.sh
    ./install-mysql.sh
    
    log_success "MySQL 8.0安装完成"
}

# 安装Redis
install_redis() {
    log_info "开始安装Redis..."

    chmod +x install-redis.sh
    ./install-redis.sh

    log_success "Redis安装完成"
}

# 安装PostgreSQL
install_postgresql() {
    log_info "开始安装PostgreSQL..."

    chmod +x install-postgresql.sh
    ./install-postgresql.sh

    log_success "PostgreSQL安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查firewalld是否运行
    if systemctl is-active --quiet firewalld; then
        log_info "配置firewalld规则..."
        sudo firewall-cmd --permanent --add-port=3306/tcp  # MySQL
        sudo firewall-cmd --permanent --add-port=6379/tcp  # Redis
        sudo firewall-cmd --permanent --add-port=5432/tcp  # PostgreSQL
        sudo firewall-cmd --reload
        log_success "防火墙规则配置完成"
    else
        log_warning "firewalld未运行，请手动配置防火墙规则"
    fi
}

# 设置开机自启动
setup_autostart() {
    log_info "设置服务开机自启动..."
    
    # Docker已在安装脚本中设置自启动
    # 容器的restart policy已设置为unless-stopped
    
    log_success "开机自启动配置完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装结果..."
    
    # 检查Docker
    if sudo docker --version &> /dev/null; then
        log_success "Docker: $(sudo docker --version)"
    else
        log_error "Docker安装失败"
        return 1
    fi
    
    # 检查MySQL容器
    if sudo docker ps | grep -q mysql8; then
        log_success "MySQL容器运行正常"
    else
        log_error "MySQL容器未运行"
        return 1
    fi
    
    # 检查Redis容器
    if sudo docker ps | grep -q redis; then
        log_success "Redis容器运行正常"
    else
        log_error "Redis容器未运行"
        return 1
    fi

    # 检查PostgreSQL容器
    if sudo docker ps | grep -q postgresql; then
        log_success "PostgreSQL容器运行正常"
    else
        log_error "PostgreSQL容器未运行"
        return 1
    fi

    log_success "所有服务验证通过"
}

# 显示安装信息
show_installation_info() {
    echo ""
    echo -e "${GREEN}==================== 安装完成 ====================${NC}"
    echo ""
    echo -e "${BLUE}MySQL 8.0 连接信息:${NC}"
    echo "  主机: localhost"
    echo "  端口: 3306"
    echo "  Root密码: MyStrongPassword123!"
    echo "  数据库: myapp"
    echo "  用户: appuser"
    echo "  用户密码: AppPassword123!"
    echo ""
    echo -e "${BLUE}Redis 连接信息:${NC}"
    echo "  主机: localhost"
    echo "  端口: 6379"
    echo "  密码: RedisPassword123!"
    echo ""
    echo -e "${BLUE}PostgreSQL 连接信息:${NC}"
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  超级用户: postgres"
    echo "  超级用户密码: PostgresPassword123!"
    echo "  数据库: myapp"
    echo "  应用用户: appuser"
    echo "  应用用户密码: AppUserPassword123!"
    echo ""
    echo -e "${BLUE}管理命令:${NC}"
    echo "  查看容器状态: ./manage-containers.sh status"
    echo "  启动所有容器: ./manage-containers.sh start"
    echo "  停止所有容器: ./manage-containers.sh stop"
    echo "  查看日志: ./manage-containers.sh logs"
    echo "  备份数据: ./manage-containers.sh backup"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo "1. 请修改默认密码以提高安全性"
    echo "2. 定期备份数据"
    echo "3. 监控容器资源使用情况"
    echo "4. 如果用户被添加到docker组，需要重新登录生效"
    echo ""
    echo -e "${GREEN}=================================================${NC}"
}

# 主安装流程
main() {
    echo -e "${BLUE}CentOS 7.9 Docker + MySQL 8.0 + Redis 一键安装脚本${NC}"
    echo ""
    
    # 检查权限和系统
    check_privileges
    check_system
    
    # 确认安装
    echo -e "${YELLOW}即将安装以下组件:${NC}"
    echo "- Docker CE"
    echo "- MySQL 8.0 (Docker容器)"
    echo "- Redis (Docker容器)"
    echo "- PostgreSQL 15 (Docker容器)"
    echo ""
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi
    
    # 执行安装
    install_docker
    install_mysql
    install_redis
    install_postgresql
    configure_firewall
    setup_autostart
    
    # 设置管理脚本权限
    chmod +x manage-containers.sh
    
    # 验证安装
    if verify_installation; then
        show_installation_info
    else
        log_error "安装验证失败，请检查错误信息"
        exit 1
    fi
}

# 运行主程序
main "$@"
