

让我为您创建一个完整的安装脚本和配置文件。首先创建Docker安装脚本：

现在创建MySQL 8.0的Docker安装和配置脚本：

现在创建Redis的Docker安装和配置脚本：

现在创建一个容器管理脚本：

现在创建一个一键安装脚本：

最后创建一个README文档：

现在让我为所有脚本设置执行权限：

我注意到您在Windows环境中，让我查看一下当前的文件：

完美！我已经为您创建了在CentOS 7.9环境中使用Docker安装MySQL 8.0和Redis的完整解决方案。以下是我为您准备的文件：

## 📁 创建的文件

1. **`install-all.sh`** - 一键安装脚本（推荐使用）
2. **`install-docker.sh`** - Docker CE安装脚本
3. **`install-mysql.sh`** - MySQL 8.0 Docker容器安装脚本
4. **`install-redis.sh`** - Redis Docker容器安装脚本
5. **`manage-containers.sh`** - 容器管理脚本
6. **`README.md`** - 详细的安装和使用说明

## 🚀 使用方法

### 方法一：一键安装（推荐）
```bash
# 在CentOS 7.9系统中执行
chmod +x install-all.sh
./install-all.sh
```

### 方法二：分步安装
```bash
# 1. 安装Docker
chmod +x install-docker.sh
./install-docker.sh

# 2. 安装MySQL 8.0
chmod +x install-mysql.sh
./install-mysql.sh

# 3. 安装Redis
chmod +x install-redis.sh
./install-redis.sh
```

## 🔧 默认配置信息

### MySQL 8.0
- **端口**: 3306
- **Root密码**: MyStrongPassword123!
- **数据库**: myapp
- **用户**: appuser / AppPassword123!
- **数据目录**: /opt/mysql/data

### Redis
- **端口**: 6379
- **密码**: RedisPassword123!
- **数据目录**: /opt/redis/data

## 🛠️ 容器管理

安装完成后，使用管理脚本：

````bash path=manage-containers.sh mode=EXCERPT
# 查看容器状态
./manage-containers.sh status

# 启动/停止/重启容器
./manage-containers.sh start|stop|restart

# 查看日志
./manage-containers.sh logs

# 备份数据
./manage-containers.sh backup
````

## ✨ 主要特性

1. **自动化安装** - 一键完成所有组件安装
2. **持久化存储** - 数据持久化到宿主机目录
3. **安全配置** - 包含密码保护和安全设置
4. **开机自启** - 容器自动重启策略
5. **完整管理** - 提供容器管理、备份、监控功能
6. **防火墙配置** - 自动配置必要的端口规则

## 🔐 安全提醒

安装完成后，请务必：
1. 修改默认密码
2. 配置防火墙规则
3. 定期备份数据
4. 监控系统资源使用

现在您可以将这些文件传输到您的CentOS 7.9服务器上，然后运行 `./install-all.sh` 即可完成整个安装过程！