-- SQL脚本：更新oss_image_url字段的URL前缀
-- 将阿里云OSS URL前缀替换为新的服务器地址前缀
-- 原前缀：https://omgbuy.oss-eu-central-1.aliyuncs.com/
-- 新前缀：http://47.254.181.212/oss/

-- 方法1：使用REPLACE函数进行替换
UPDATE your_table_name 
SET oss_image_url = REPLACE(
    oss_image_url, 
    'https://omgbuy.oss-eu-central-1.aliyuncs.com/', 
    'http://47.254.181.212/oss/'
)
WHERE oss_image_url LIKE 'https://omgbuy.oss-eu-central-1.aliyuncs.com/%';

-- 方法2：使用REGEXP_REPLACE函数（适用于PostgreSQL）
-- UPDATE your_table_name 
-- SET oss_image_url = REGEXP_REPLACE(
--     oss_image_url, 
--     '^https://omgbuy\.oss-eu-central-1\.aliyuncs\.com/', 
--     'http://47.254.181.212/oss/'
-- )
-- WHERE oss_image_url ~ '^https://omgbuy\.oss-eu-central-1\.aliyuncs\.com/';

-- 方法3：使用SUBSTRING和CONCAT函数
-- UPDATE your_table_name 
-- SET oss_image_url = CONCAT(
--     'http://47.254.181.212/oss/', 
--     SUBSTRING(oss_image_url FROM 49)  -- 49是原前缀的长度+1
-- )
-- WHERE oss_image_url LIKE 'https://omgbuy.oss-eu-central-1.aliyuncs.com/%';

-- 执行前建议先查看要更新的数据
-- SELECT id, oss_image_url 
-- FROM your_table_name 
-- WHERE oss_image_url LIKE 'https://omgbuy.oss-eu-central-1.aliyuncs.com/%';

-- 执行后验证更新结果
-- SELECT id, oss_image_url 
-- FROM your_table_name 
-- WHERE oss_image_url LIKE 'http://47.254.181.212/oss/%';

-- 注意事项：
-- 1. 请将 'your_table_name' 替换为实际的表名
-- 2. 建议在执行UPDATE前先备份数据
-- 3. 可以先在测试环境中验证脚本的正确性
-- 4. 如果数据量很大，建议分批执行或在维护窗口期间执行
