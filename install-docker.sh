#!/bin/bash

# CentOS 7.9 Docker安装脚本
# 作者: wujule
# 日期: $(date)

set -e

echo "开始在CentOS 7.9上安装Docker CE..."

# 检查系统版本
echo "检查系统版本..."
cat /etc/redhat-release

# 卸载旧版本Docker（如果存在）
echo "卸载旧版本Docker..."
sudo yum remove -y docker \
                  docker-client \
                  docker-client-latest \
                  docker-common \
                  docker-latest \
                  docker-latest-logrotate \
                  docker-logrotate \
                  docker-engine

# 安装必要的依赖包
echo "安装必要的依赖包..."
sudo yum install -y yum-utils \
                    device-mapper-persistent-data \
                    lvm2

# 添加Docker官方仓库
echo "添加Docker官方仓库..."
sudo yum-config-manager \
    --add-repo \
    https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker CE
echo "安装Docker CE..."
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 启动Docker服务
echo "启动Docker服务..."
sudo systemctl start docker

# 设置Docker开机自启动
echo "设置Docker开机自启动..."
sudo systemctl enable docker

# 将当前用户添加到docker组（可选）
echo "将当前用户添加到docker组..."
sudo usermod -aG docker $USER

# 验证Docker安装
echo "验证Docker安装..."
sudo docker --version
sudo docker info

# 测试Docker运行
echo "测试Docker运行..."
sudo docker run hello-world

echo "Docker安装完成！"
echo "注意：如果将用户添加到docker组，需要重新登录才能生效"
echo "或者使用 'newgrp docker' 命令刷新组权限"
