#!/bin/bash

# Redis Docker安装脚本
# 作者: AI Assistant
# 日期: $(date)

set -e

# 配置变量
REDIS_PASSWORD="RedisPassword123!"
REDIS_PORT=6379
CONTAINER_NAME="redis"
REDIS_DATA_DIR="/opt/redis/data"
REDIS_CONFIG_DIR="/opt/redis/conf"

echo "开始安装Redis Docker容器..."

# 创建数据目录
echo "创建Redis数据和配置目录..."
sudo mkdir -p $REDIS_DATA_DIR
sudo mkdir -p $REDIS_CONFIG_DIR

# 创建Redis配置文件
echo "创建Redis配置文件..."
sudo tee $REDIS_CONFIG_DIR/redis.conf > /dev/null <<EOF
# Redis配置文件

# 网络设置
bind 0.0.0.0
port 6379
protected-mode yes

# 安全设置
requirepass $REDIS_PASSWORD

# 持久化设置
save 900 1
save 300 10
save 60 10000

# RDB文件设置
dbfilename dump.rdb
dir /data

# AOF设置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 内存设置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 日志设置
loglevel notice
logfile ""

# 客户端设置
timeout 300
tcp-keepalive 300
tcp-backlog 511

# 数据库设置
databases 16

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100

# 通知设置
notify-keyspace-events ""

# 哈希设置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表设置
list-max-ziplist-size -2
list-compress-depth 0

# 集合设置
set-max-intset-entries 512

# 有序集合设置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog设置
hll-sparse-max-bytes 3000

# 流设置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# 频率
hz 10

# 动态频率
dynamic-hz yes

# AOF重写设置
aof-rewrite-incremental-fsync yes

# RDB校验和
rdbchecksum yes

# 停止写入磁盘错误
stop-writes-on-bgsave-error yes

# RDB压缩
rdbcompression yes

# 内存使用
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no
EOF

# 设置目录权限
sudo chown -R 999:999 $REDIS_DATA_DIR
sudo chown -R 999:999 $REDIS_CONFIG_DIR

# 拉取Redis镜像
echo "拉取Redis镜像..."
sudo docker pull redis:latest

# 停止并删除已存在的容器（如果有）
if sudo docker ps -a | grep -q $CONTAINER_NAME; then
    echo "停止并删除已存在的Redis容器..."
    sudo docker stop $CONTAINER_NAME || true
    sudo docker rm $CONTAINER_NAME || true
fi

# 创建并启动Redis容器
echo "创建并启动Redis容器..."
sudo docker run -d \
    --name $CONTAINER_NAME \
    --restart unless-stopped \
    -p $REDIS_PORT:6379 \
    -v $REDIS_DATA_DIR:/data \
    -v $REDIS_CONFIG_DIR/redis.conf:/usr/local/etc/redis/redis.conf \
    redis:latest redis-server /usr/local/etc/redis/redis.conf

# 等待Redis启动
echo "等待Redis启动..."
sleep 10

# 检查容器状态
echo "检查Redis容器状态..."
sudo docker ps | grep $CONTAINER_NAME

# 测试连接
echo "测试Redis连接..."
sudo docker exec $CONTAINER_NAME redis-cli -a $REDIS_PASSWORD ping

echo "Redis安装完成！"
echo "连接信息："
echo "  主机: localhost"
echo "  端口: $REDIS_PORT"
echo "  密码: $REDIS_PASSWORD"
echo "  数据目录: $REDIS_DATA_DIR"
echo "  配置文件: $REDIS_CONFIG_DIR/redis.conf"
