#!/bin/bash

# MySQL容器重启问题修复脚本
# 作者: wujule

set -e

# 配置变量
MYSQL_ROOT_PASSWORD="MyStrongPassword123!"
MYSQL_DATABASE="myapp"
MYSQL_USER="appuser"
MYSQL_PASSWORD="AppPassword123!"
CONTAINER_NAME="mysql8"
MYSQL_DATA_DIR="/opt/mysql/data"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}MySQL容器重启问题修复脚本${NC}"
echo ""

# 检查容器状态
echo -e "${BLUE}检查当前容器状态...${NC}"
if sudo docker ps -a | grep -q $CONTAINER_NAME; then
    container_status=$(sudo docker inspect --format='{{.State.Status}}' $CONTAINER_NAME)
    restart_count=$(sudo docker inspect --format='{{.RestartCount}}' $CONTAINER_NAME)
    echo "容器状态: $container_status"
    echo "重启次数: $restart_count"
    
    if [ "$restart_count" -gt 5 ]; then
        echo -e "${RED}容器重启次数过多，需要修复${NC}"
    fi
else
    echo -e "${RED}容器不存在${NC}"
    exit 1
fi

# 备份当前配置
echo -e "${BLUE}备份当前配置...${NC}"
if [ -f "/opt/mysql/conf/my.cnf" ]; then
    sudo cp /opt/mysql/conf/my.cnf /opt/mysql/conf/my.cnf.backup.$(date +%Y%m%d_%H%M%S)
    echo "配置文件已备份"
fi

# 停止并删除容器
echo -e "${YELLOW}停止并删除当前容器...${NC}"
sudo docker stop $CONTAINER_NAME || true
sudo docker rm $CONTAINER_NAME || true

# 创建简化的MySQL容器（不使用自定义配置）
echo -e "${GREEN}创建新的MySQL容器（使用默认配置）...${NC}"
sudo docker run -d \
    --name $CONTAINER_NAME \
    --restart unless-stopped \
    -p 3306:3306 \
    -e MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD \
    -e MYSQL_DATABASE=$MYSQL_DATABASE \
    -e MYSQL_USER=$MYSQL_USER \
    -e MYSQL_PASSWORD=$MYSQL_PASSWORD \
    -v $MYSQL_DATA_DIR:/var/lib/mysql \
    mysql:8.0

echo "新容器已创建"

# 等待MySQL启动
echo -e "${BLUE}等待MySQL启动...${NC}"
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "等待MySQL启动... ($attempt/$max_attempts)"
    
    if sudo docker exec $CONTAINER_NAME mysqladmin ping -uroot -p$MYSQL_ROOT_PASSWORD --silent 2>/dev/null; then
        echo -e "${GREEN}MySQL已成功启动！${NC}"
        break
    fi
    
    # 检查容器是否还在运行
    if ! sudo docker ps | grep -q $CONTAINER_NAME; then
        echo -e "${RED}容器启动失败${NC}"
        sudo docker logs $CONTAINER_NAME
        exit 1
    fi
    
    sleep 10
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo -e "${RED}MySQL启动超时${NC}"
    sudo docker logs $CONTAINER_NAME
    exit 1
fi

# 测试连接
echo -e "${BLUE}测试MySQL连接...${NC}"
if sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SELECT VERSION();" 2>/dev/null; then
    echo -e "${GREEN}✓ MySQL连接测试成功${NC}"
else
    echo -e "${RED}✗ MySQL连接测试失败${NC}"
    exit 1
fi

# 显示数据库
echo -e "${BLUE}显示数据库列表:${NC}"
sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SHOW DATABASES;" 2>/dev/null

echo ""
echo -e "${GREEN}=== 修复完成 ===${NC}"
echo "MySQL容器已成功修复并正常运行"
echo ""
echo -e "${BLUE}连接信息:${NC}"
echo "主机: localhost"
echo "端口: 3306"
echo "Root密码: $MYSQL_ROOT_PASSWORD"
echo "数据库: $MYSQL_DATABASE"
echo "用户: $MYSQL_USER"
echo "用户密码: $MYSQL_PASSWORD"
echo ""
echo -e "${YELLOW}注意: 当前使用默认配置，如需自定义配置请在确认正常运行后再添加${NC}"
