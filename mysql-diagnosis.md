# MySQL连接问题诊断和解决方案

## 问题分析

您遇到的错误 `ERROR 2002 (HY000): Can't connect to local MySQL server through socket '/var/run/mysqld/mysqld.sock' (2)` 是一个常见问题，通常发生在以下情况：

1. **MySQL还未完全初始化完成** - 这是最常见的原因
2. **容器内部网络问题**
3. **MySQL配置问题**

## 立即解决方案

### 1. 检查容器状态
```bash
# 检查容器是否正在运行
sudo docker ps | grep mysql8

# 查看容器详细状态
sudo docker inspect mysql8 | grep -A 5 -B 5 "Status"
```

### 2. 查看MySQL日志
```bash
# 查看MySQL容器日志
sudo docker logs mysql8

# 实时查看日志
sudo docker logs -f mysql8
```

### 3. 等待MySQL完全启动
MySQL 8.0首次启动时需要初始化数据库，这个过程可能需要2-5分钟。请耐心等待。

```bash
# 使用mysqladmin检查MySQL是否准备就绪
sudo docker exec mysql8 mysqladmin ping -uroot -pMyStrongPassword123! --silent

# 如果上面命令成功（没有输出），则MySQL已准备就绪
echo $?  # 返回0表示成功
```

### 4. 正确的连接测试方法
```bash
# 方法1: 使用mysqladmin ping（推荐）
sudo docker exec mysql8 mysqladmin ping -uroot -pMyStrongPassword123!

# 方法2: 使用TCP连接而不是socket
sudo docker exec mysql8 mysql -h127.0.0.1 -P3306 -uroot -pMyStrongPassword123! -e "SELECT VERSION();"

# 方法3: 直接进入MySQL shell
sudo docker exec -it mysql8 mysql -uroot -pMyStrongPassword123!
```

## 自动化修复脚本

创建一个等待脚本：

```bash
#!/bin/bash
# wait-for-mysql.sh

MYSQL_ROOT_PASSWORD="MyStrongPassword123!"
CONTAINER_NAME="mysql8"

echo "等待MySQL启动..."
for i in {1..60}; do
    if sudo docker exec $CONTAINER_NAME mysqladmin ping -uroot -p$MYSQL_ROOT_PASSWORD --silent 2>/dev/null; then
        echo "MySQL已启动！"
        sudo docker exec $CONTAINER_NAME mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "SELECT VERSION();"
        exit 0
    fi
    echo "等待中... ($i/60)"
    sleep 5
done

echo "MySQL启动超时"
sudo docker logs $CONTAINER_NAME
exit 1
```

## 如果问题持续存在

### 1. 重启容器
```bash
sudo docker restart mysql8
```

### 2. 检查系统资源
```bash
# 检查内存使用
free -h

# 检查磁盘空间
df -h

# 检查容器资源使用
sudo docker stats mysql8 --no-stream
```

### 3. 重新创建容器
如果问题持续，可能需要重新创建容器：

```bash
# 停止并删除容器
sudo docker stop mysql8
sudo docker rm mysql8

# 重新运行安装脚本
./install-mysql.sh
```

### 4. 检查配置文件
```bash
# 检查MySQL配置文件
cat /opt/mysql/conf/my.cnf

# 检查数据目录权限
ls -la /opt/mysql/data
```

## 预防措施

1. **增加等待时间** - 修改安装脚本中的等待时间
2. **使用健康检查** - 在Docker容器中添加健康检查
3. **监控资源** - 确保系统有足够的内存和磁盘空间

## 连接信息确认

安装成功后，您应该能够使用以下信息连接：

- **主机**: localhost
- **端口**: 3306
- **Root密码**: MyStrongPassword123!
- **数据库**: myapp
- **用户**: appuser
- **用户密码**: AppPassword123!

## 下一步

1. 首先运行 `sudo docker logs mysql8` 查看详细日志
2. 等待几分钟让MySQL完全初始化
3. 使用 `sudo docker exec mysql8 mysqladmin ping -uroot -pMyStrongPassword123!` 测试连接
4. 如果仍有问题，请提供日志输出以便进一步诊断
