
## Discord链接识别机器人开发步骤

我已经为您创建了一个完整的Discord链接识别机器人项目。以下是使用该机器人的步骤：

### 一、准备工作

1. 在[Discord开发者平台](https://discord.com/developers/applications)创建应用
   - 创建新应用
   - 在Bot部分创建机器人
   - 开启所有必要的Intents（特别是Message Content Intent）
   - 复制机器人令牌(BOT_TOKEN)和应用ID(CLIENT_ID)

2. 配置环境变量
   - 复制`example.env`文件为`.env`
   - 填入您的Discord机器人令牌和应用ID
   - 设置外部API的URL和密钥

### 二、安装和运行

1. 安装依赖包
   ```
   npm install
   ```

2. 部署斜杠命令
   ```
   npm run deploy
   ```

3. 启动机器人
   ```
   npm start
   ```

### 三、使用方法

1. 自动检测链接
   - 机器人会自动检测用户消息中的链接
   - 分析链接并返回结果

2. 使用斜杠命令
   ```
   /analyze url:<链接地址>
   ```

### 四、项目结构说明

- `config.js`: 配置文件，包含机器人和API配置
- `utils/linkDetector.js`: 链接检测工具
- `services/apiService.js`: 外部API调用服务
- `commands/analyze.js`: 分析命令处理
- `events/messageCreate.js`: 消息事件处理
- `deploy-commands.js`: 命令部署脚本
- `index.js`: 主程序入口

### 五、自定义开发

如需调整链接分析逻辑或扩展功能，可以修改：
- `services/apiService.js` 中的API调用方式
- `utils/linkDetector.js` 中的链接检测逻辑
- 添加更多命令到`commands`目录

该机器人已为您准备好基础框架，您只需配置好环境变量，安装依赖并运行即可使用。